import React, { useState, useEffect } from "react";
import { Button, Modal } from "react-bootstrap";
import {
  useLazyGetRefundStatusQuery,
  useRefundOrderTransactionMutation,
} from "../../services/order";
import { currencySymbol } from "../../utils/constant/currency";
import { getAllErrorMessages } from "../../utils/errors";
import { toast } from "react-toastify";

interface RefundButtonProps {
  orderId: string;
  orderCurrency?: string;
  onRefundSuccess?: () => void;
}

export const RefundButton: React.FC<RefundButtonProps> = ({
  orderId,
  orderCurrency = "USD",
  onRefundSuccess,
}) => {
  const [refundStatus, setRefundStatus] =
    useState<TRefundStatusResponse | null>(null);
  const [isLoadingStatus, setIsLoadingStatus] = useState(false);
  const [isRefunding, setIsRefunding] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const [getRefundStatus] = useLazyGetRefundStatusQuery();
  const [refundTransaction] = useRefundOrderTransactionMutation();

  useEffect(() => {
    if (orderId && orderId.trim() !== "") {
      loadRefundStatus();
    }
  }, [orderId]);

  const loadRefundStatus = async () => {
    setIsLoadingStatus(true);
    try {
      const result = await getRefundStatus(orderId).unwrap();
      setRefundStatus(result);
    } catch (error: any) {
      console.error("Error loading refund status:", error);
      // Hiển thị thông báo lỗi thân thiện hơn
      const errorMessage =
        getAllErrorMessages(error).messages[0] ||
        "Không thể tải thông tin hoàn tiền. Vui lòng thử lại.";
      toast.error(errorMessage);
    } finally {
      setIsLoadingStatus(false);
    }
  };

  if (!orderId || orderId.trim() === "" || !refundStatus?.canRefund) {
    return null;
  }

  const handleRefundClick = () => {
    if (!orderId || orderId.trim() === "") {
      console.error(
        "Cannot proceed with refund: orderId is empty or undefined"
      );
      return;
    }
    setShowConfirmModal(true);
  };

  const handleConfirmRefund = async () => {
    if (!orderId || orderId.trim() === "") {
      toast.error("Không thể thực hiện hoàn tiền: Thiếu thông tin đơn hàng");
      return;
    }

    setIsRefunding(true);
    try {
      const result = await refundTransaction(orderId).unwrap();
      setShowConfirmModal(false);

      // Hiển thị thông báo thành công với thông tin chi tiết
      if (result.success) {
        toast.success("Hoàn tiền thành công! Giao dịch đã được xử lý.");
      } else {
        toast.warning("Hoàn tiền đã được gửi nhưng cần xác nhận từ ngân hàng.");
      }

      await loadRefundStatus();
      onRefundSuccess?.();
    } catch (error: any) {
      console.error("Refund failed:", error);

      // Xử lý các loại lỗi khác nhau
      let errorMessage = "Không thể thực hiện hoàn tiền. Vui lòng thử lại sau.";

      if (error?.status === 400) {
        errorMessage =
          getAllErrorMessages(error).messages[0] ||
          "Đơn hàng không đủ điều kiện hoàn tiền.";
      } else if (error?.status === 403) {
        errorMessage =
          "Bạn không có quyền thực hiện hoàn tiền cho đơn hàng này.";
      } else if (error?.status === 404) {
        errorMessage = "Không tìm thấy đơn hàng hoặc giao dịch.";
      } else if (error?.status >= 500) {
        errorMessage = "Lỗi hệ thống. Vui lòng liên hệ bộ phận kỹ thuật.";
      } else if (getAllErrorMessages(error).messages[0]) {
        errorMessage = getAllErrorMessages(error).messages[0];
      }

      toast.error(errorMessage);
    } finally {
      setIsRefunding(false);
    }
  };

  const handleCancelRefund = () => {
    setShowConfirmModal(false);
  };

  return (
    <>
      <Button
        variant="danger"
        size="sm"
        className="ms-2 d-flex align-items-center"
        onClick={handleRefundClick}
        disabled={isLoadingStatus || isRefunding}
      >
        {isLoadingStatus || isRefunding ? (
          <>
            <i className="spinner-border spinner-border-sm me-2" />
            <span>Processing...</span>
          </>
        ) : (
          <>
            <i className="bi-arrow-return-left me-1" />
            <span>Refund</span>
          </>
        )}
      </Button>

      <Modal
        show={showConfirmModal}
        onHide={handleCancelRefund}
        centered
        size="lg"
        className="custom-modal"
      >
        <Modal.Header closeButton className="border-bottom-0 pb-0">
          <Modal.Title className="d-flex align-items-center">
            <i className="bi-exclamation-triangle text-warning me-2 fs-4" />
            <span className="fw-semibold">Confirm Refund</span>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="pt-2">
          <div className="mb-4">
            <p className="mb-2 fs-6">
              Are you sure you want to refund this transaction?
            </p>
            <p className="text-muted small mb-0">
              This action cannot be undone.
            </p>
          </div>

          {refundStatus?.transaction && (
            <div className="card border-0 bg-light mb-3">
              <div className="card-body p-3">
                <h6 className="card-title mb-2 fw-semibold">
                  Transaction Details
                </h6>
                <div className="row g-2">
                  <div className="col-4">
                    <small className="text-muted">Amount:</small>
                  </div>
                  <div className="col-8">
                    <small className="fw-medium">
                      {currencySymbol[orderCurrency]}{" "}
                      {refundStatus.transaction.amount}
                    </small>
                  </div>
                  <div className="col-4">
                    <small className="text-muted">Source:</small>
                  </div>
                  <div className="col-8">
                    <small className="fw-medium">
                      {refundStatus.transaction.source}
                    </small>
                  </div>
                  <div className="col-4">
                    <small className="text-muted">ID:</small>
                  </div>
                  <div className="col-8">
                    <small className="fw-medium font-monospace">
                      {refundStatus.transaction.sourceId}
                    </small>
                  </div>
                </div>
              </div>
            </div>
          )}

          {refundStatus?.refundReason && (
            <div className="alert alert-info border-0 bg-info-subtle">
              <div className="d-flex">
                <i className="bi-info-circle text-info me-2 mt-1" />
                <div>
                  <strong className="text-info">Note:</strong>
                  <span className="ms-1">{refundStatus.refundReason}</span>
                </div>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="border-top-0 pt-0">
          <Button
            variant="light"
            className="border"
            onClick={handleCancelRefund}
            disabled={isRefunding}
          >
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleConfirmRefund}
            disabled={isRefunding}
            className="ms-2"
          >
            {isRefunding ? (
              <>
                <i className="spinner-border spinner-border-sm me-2" />
                Processing...
              </>
            ) : (
              "Confirm Refund"
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
