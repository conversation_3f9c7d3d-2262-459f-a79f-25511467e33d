import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import {
  useLazyGetRefundStatusQuery,
  useRefundOrderTransactionMutation,
} from "../../services/order";
import { currencySymbol } from "../../utils/constant/currency";
import { toast } from "react-toastify";

interface RefundButtonProps {
  orderId: string;
  orderCurrency?: string;
  onRefundSuccess?: () => void;
}

export const RefundButton: React.FC<RefundButtonProps> = ({
  orderId,
  orderCurrency = "USD",
  onRefundSuccess,
}) => {
  const [refundStatus, setRefundStatus] =
    useState<TRefundStatusResponse | null>(null);
  const [isLoadingStatus, setIsLoadingStatus] = useState(false);
  const [isRefunding, setIsRefunding] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const [getRefundStatus] = useLazyGetRefundStatusQuery();
  const [refundTransaction] = useRefundOrderTransactionMutation();

  useEffect(() => {
    if (orderId && orderId.trim() !== "") {
      loadRefundStatus();
    }
  }, [orderId]);

  const loadRefundStatus = async () => {
    setIsLoadingStatus(true);
    try {
      const result = await getRefundStatus(orderId).unwrap();
      setRefundStatus(result);
    } catch (error: any) {
      console.error("Error loading refund status:", error);
      console.log("Load refund status error structure:", {
        status: error?.status,
        data: error?.data,
        message: error?.message,
        response: error?.response,
      });

      // Xử lý error response theo tài liệu API
      let errorMessage = "Không thể tải thông tin hoàn tiền. Vui lòng thử lại.";

      // Kiểm tra error response structure theo tài liệu
      // RTK Query có thể wrap error trong nhiều cấu trúc khác nhau
      const apiErrorMessage =
        error?.data?.message ||
        error?.message ||
        error?.error?.message ||
        error?.response?.data?.message;

      if (error?.status === 400) {
        errorMessage = apiErrorMessage || "Yêu cầu không hợp lệ.";
      } else if (error?.status === 401) {
        errorMessage = "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.";
      } else if (error?.status === 403) {
        errorMessage = "Bạn không có quyền truy cập thông tin đơn hàng này.";
      } else if (error?.status === 404) {
        errorMessage = "Không tìm thấy đơn hàng.";
      } else if (error?.status >= 500) {
        errorMessage = "Lỗi hệ thống. Vui lòng liên hệ bộ phận kỹ thuật.";
      } else if (apiErrorMessage) {
        errorMessage = apiErrorMessage;
      }

      toast.error(errorMessage);
    } finally {
      setIsLoadingStatus(false);
    }
  };

  if (!orderId || orderId.trim() === "" || !refundStatus?.canRefund) {
    return null;
  }

  const handleRefundClick = () => {
    if (!orderId || orderId.trim() === "") {
      console.error(
        "Cannot proceed with refund: orderId is empty or undefined"
      );
      return;
    }
    setShowConfirmModal(true);
  };

  const handleConfirmRefund = async () => {
    if (!orderId || orderId.trim() === "") {
      toast.error("Cannot proceed with refund: Missing order information");
      return;
    }

    setIsRefunding(true);
    try {
      const result = await refundTransaction(orderId).unwrap();
      setShowConfirmModal(false);

      if (result.success) {
        toast.success("Refund successful! Transaction has been processed.");
      } else {
        toast.warning(
          "Refund has been submitted but requires bank confirmation."
        );
      }

      await loadRefundStatus();
      onRefundSuccess?.();
    } catch (error: any) {
      console.error("Refund failed:", error);
      console.log("Error structure:", {
        status: error?.status,
        data: error?.data,
        message: error?.message,
        response: error?.response,
      });

      // Xử lý error response theo tài liệu API
      let errorMessage = "Không thể thực hiện hoàn tiền. Vui lòng thử lại sau.";

      // Kiểm tra error response structure theo tài liệu
      // RTK Query có thể wrap error trong nhiều cấu trúc khác nhau
      const apiErrorMessage =
        error?.data?.message ||
        error?.message ||
        error?.error?.message ||
        error?.response?.data?.message;

      if (error?.status === 400) {
        // Sử dụng message từ API response trước
        errorMessage =
          apiErrorMessage || "Đơn hàng không đủ điều kiện hoàn tiền.";
      } else if (error?.status === 401) {
        errorMessage = "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.";
      } else if (error?.status === 403) {
        errorMessage =
          "Bạn không có quyền thực hiện hoàn tiền cho đơn hàng này.";
      } else if (error?.status === 404) {
        errorMessage = "Không tìm thấy đơn hàng hoặc giao dịch.";
      } else if (error?.status >= 500) {
        errorMessage = "Lỗi hệ thống. Vui lòng liên hệ bộ phận kỹ thuật.";
      } else if (apiErrorMessage) {
        // Fallback to API message if available
        errorMessage = apiErrorMessage;
      }

      toast.error(errorMessage);
    } finally {
      setIsRefunding(false);
    }
  };

  const handleCancelRefund = () => {
    setShowConfirmModal(false);
  };

  return (
    <>
      <Button
        variant="danger"
        size="sm"
        className="ms-2 d-flex align-items-center fs-12"
        onClick={handleRefundClick}
        disabled={isLoadingStatus || isRefunding}
      >
        {isLoadingStatus || isRefunding ? (
          <>
            <i className="spinner-border spinner-border-sm me-2" />
            <span>Processing...</span>
          </>
        ) : (
          <>
            <i className="bi-arrow-return-left me-1 fs-12" />
            <span>Refund</span>
          </>
        )}
      </Button>

      <Modal
        show={showConfirmModal}
        onHide={handleCancelRefund}
        centered
        size="lg"
        className="custom-modal"
      >
        <Modal.Header closeButton>
          <Modal.Title className="d-flex align-items-center fw-semibold">
            <i className="bi-exclamation-triangle text-warning me-2 fs-18" />
            <span>Confirm Refund</span>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-4">
            <p className="mb-2 fs-14 fw-medium">
              Are you sure you want to refund this transaction?
            </p>
            <p className="text-muted fs-12 mb-0">
              This action cannot be undone.
            </p>
          </div>

          {refundStatus?.transaction && (
            <div className="card border-0 bg-light mb-3">
              <div className="card-body p-3">
                <h6 className="card-title mb-3 fw-semibold fs-14">
                  Transaction Details
                </h6>
                <div className="row g-2">
                  <div className="col-4">
                    <span className="text-muted fs-12">Amount:</span>
                  </div>
                  <div className="col-8">
                    <span className="fw-medium fs-12">
                      {currencySymbol[orderCurrency]}{" "}
                      {refundStatus.transaction.amount}
                    </span>
                  </div>
                  <div className="col-4">
                    <span className="text-muted fs-12">Source:</span>
                  </div>
                  <div className="col-8">
                    <span className="fw-medium fs-12">
                      {refundStatus.transaction.source}
                    </span>
                  </div>
                  <div className="col-4">
                    <span className="text-muted fs-12">ID:</span>
                  </div>
                  <div className="col-8">
                    <span className="fw-medium fs-11 font-monospace">
                      {refundStatus.transaction.sourceId}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {refundStatus?.refundReason && (
            <div className="alert alert-info border-0 bg-info-subtle">
              <div className="d-flex">
                <i className="bi-info-circle text-info me-2 mt-1 fs-14" />
                <div>
                  <strong className="text-info fs-13">Note:</strong>
                  <span className="ms-1 fs-13">
                    {refundStatus.refundReason}
                  </span>
                </div>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="light"
            className="border fs-13"
            onClick={handleCancelRefund}
            disabled={isRefunding}
          >
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleConfirmRefund}
            disabled={isRefunding}
            className="ms-2 fs-13"
          >
            {isRefunding ? (
              <>
                <i className="spinner-border spinner-border-sm me-2" />
                <span>Processing...</span>
              </>
            ) : (
              <span>Confirm Refund</span>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
