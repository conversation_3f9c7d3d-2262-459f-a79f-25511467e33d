import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col } from "react-bootstrap";
import {
  useLazyGetRefundStatusQuery,
  useRefundOrderTransactionMutation,
} from "../../services/order";
import { currencySymbol } from "../../utils/constant/currency";
import { getAllErrorMessages } from "../../utils/errors";
import { toast } from "react-toastify";

interface RefundButtonProps {
  orderId: string;
  orderCurrency?: string;
  onRefundSuccess?: () => void;
}

export const RefundButton: React.FC<RefundButtonProps> = ({
  orderId,
  orderCurrency = "USD",
  onRefundSuccess,
}) => {
  const [refundStatus, setRefundStatus] =
    useState<TRefundStatusResponse | null>(null);
  const [isLoadingStatus, setIsLoadingStatus] = useState(false);
  const [isRefunding, setIsRefunding] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const [getRefundStatus] = useLazyGetRefundStatusQuery();
  const [refundTransaction] = useRefundOrderTransactionMutation();

  useEffect(() => {
    if (orderId && orderId.trim() !== "") {
      loadRefundStatus();
    }
  }, [orderId]);

  const loadRefundStatus = async () => {
    setIsLoadingStatus(true);
    try {
      const result = await getRefundStatus(orderId).unwrap();
      setRefundStatus(result);
    } catch (error: any) {
      console.error("Error loading refund status:", error);
    } finally {
      setIsLoadingStatus(false);
    }
  };

  if (!orderId || orderId.trim() === "" || !refundStatus?.canRefund) {
    return null;
  }

  const handleRefundClick = () => {
    if (!orderId || orderId.trim() === "") {
      console.error(
        "Cannot proceed with refund: orderId is empty or undefined"
      );
      return;
    }
    setShowConfirmModal(true);
  };

  const handleConfirmRefund = async () => {
    if (!orderId || orderId.trim() === "") {
      console.error(
        "Cannot proceed with refund: orderId is empty or undefined"
      );
      return;
    }

    setIsRefunding(true);
    try {
      await refundTransaction(orderId).unwrap();
      setShowConfirmModal(false);

      toast.success("Transaction has been refunded successfully");

      await loadRefundStatus();

      onRefundSuccess?.();
    } catch (error: any) {
      console.error("Refund failed:", error);
      const errorMessage =
        getAllErrorMessages(error).messages[0] || "Failed to process refund";
      toast.error(errorMessage);
    } finally {
      setIsRefunding(false);
    }
  };

  const handleCancelRefund = () => {
    setShowConfirmModal(false);
  };

  return (
    <>
      <Button
        variant="danger-light"
        className="btn-sm ms-2"
        onClick={handleRefundClick}
        disabled={isLoadingStatus || isRefunding}
      >
        {isLoadingStatus || isRefunding ? (
          <>
            <i className="spinner-border spinner-border-sm me-2" />
            Processing...
          </>
        ) : (
          <>
            <i className="bi-arrow-return-left me-1" />
            Refund
          </>
        )}
      </Button>

      <Modal
        show={showConfirmModal}
        onHide={handleCancelRefund}
        centered
        size="lg"
      >
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="bi-exclamation-triangle text-warning me-2" />
            Confirm Refund
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center mb-3">
            <p className="mb-0">
              Are you sure you want to refund this transaction?
            </p>
            <p className="text-muted small">This action cannot be undone.</p>
          </div>

          {refundStatus?.transaction && (
            <div className="bg-light p-3 rounded mb-3">
              <Row>
                <Col>
                  <div className="fw-bold">Transaction Details</div>
                  <div className="text-muted small">
                    <div>
                      Amount: {currencySymbol[orderCurrency]}{" "}
                      {refundStatus.transaction.amount}
                    </div>
                    <div>Source: {refundStatus.transaction.source}</div>
                    <div>ID: {refundStatus.transaction.sourceId}</div>
                  </div>
                </Col>
              </Row>
            </div>
          )}

          {refundStatus?.refundReason && (
            <div className="alert alert-info">
              <i className="bi-info-circle me-2" />
              <strong>Note:</strong> {refundStatus.refundReason}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="secondary"
            onClick={handleCancelRefund}
            disabled={isRefunding}
          >
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleConfirmRefund}
            disabled={isRefunding}
          >
            {isRefunding ? (
              <>
                <i className="spinner-border spinner-border-sm me-2" />
                Processing...
              </>
            ) : (
              "Confirm Refund"
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
