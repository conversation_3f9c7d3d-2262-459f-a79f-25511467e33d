type TOrder = TAppModel & {
  shopifyId: string;

  name: string;
  email: string;

  zurnoStatus: string;

  status: string;
  financialStatus: string;
  fulfillmentStatus: string;

  fulfilledAt: Date;

  orderDetailsCount: number;
  subtotalPrice: number;
  totalDiscounts: number;
  totalShipping: number;
  totalTax: number;
  totalPrice: number;
  currentTotalPrice: number;

  currency: string;

  cancelledAt: Date;
  closedAt: Date;

  userId: string;
  user: TUser;

  shippingId: string;
  shipping: TAddress;

  billingId: string;
  billing: TAddress;

  customerId: string;

  orderDetails: TOrderDetail[];

  fulfillments: TFulfillment[];
};

// Refund API Types
type TRefundStatusResponse = {
  orderId: string;
  orderName: string;
  orderStatus: string;
  financialStatus: string;
  cancelledAt: string | null;
  transaction: {
    id: string;
    status: string;
    amount: number;
    currency: string;
    source: string;
    sourceId: string;
    createdAt: string;
  } | null;
  canRefund: boolean;
  refundReason?: string;
};

type TRefundResponse = {
  success: boolean;
  transactionId?: string;
  accountNumber?: string;
  accountType?: string;
  responseCode?: string;
  messageCode?: string;
  description?: string;
  [key: string]: any; // For additional fields from payment providers
};
